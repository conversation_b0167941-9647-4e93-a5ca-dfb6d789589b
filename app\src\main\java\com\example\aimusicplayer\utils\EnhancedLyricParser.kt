package com.example.aimusicplayer.utils

import android.util.Log
import com.example.aimusicplayer.data.model.LyricInfo as KotlinLyricInfo
import com.example.aimusicplayer.data.model.LyricLine as KotlinLyricLine
import com.example.aimusicplayer.data.model.LyricInfo
import com.example.aimusicplayer.data.model.LyricLine
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.regex.Pattern
import java.util.regex.PatternSyntaxException

/**
 * 增强型歌词解析器
 * 提供更高效的歌词解析和处理功能
 */
object EnhancedLyricParser {
    private const val TAG = "EnhancedLyricParser"

    // 歌词时间标签正则表达式
    private val timeTagPattern = Pattern.compile("\\[(\\d{2}):(\\d{2})\\.(\\d{2,3})\\]")

    // 歌词信息标签正则表达式
    private val infoTagPattern = Pattern.compile("\\[(\\w+):(.+?)\\]")

    // 翻译歌词标记
    private const val TRANSLATION_SEPARATOR = "【"

    /**
     * 解析LRC格式歌词 - 性能优化版本
     * @param lrcContent LRC格式歌词内容
     * @return 解析后的歌词信息
     */
    suspend fun parseLrc(lrcContent: String): LyricInfo = withContext(Dispatchers.Default) {
        val lyricInfo = LyricInfo()

        try {
            // 预检查内容是否为空
            if (lrcContent.isBlank()) return@withContext lyricInfo

            // 按行分割歌词 - 优化：使用sequence减少内存分配
            val lines = lrcContent.lineSequence()

            // 临时存储歌词行 - 预分配容量
            val tempEntries = ArrayList<LyricLine>(100)

            // 解析每一行 - 优化：使用forEach减少迭代器开销
            lines.forEach { line ->
                val trimmedLine = line.trim()
                if (trimmedLine.isEmpty()) return@forEach

                // 尝试解析信息标签
                val infoMatcher = infoTagPattern.matcher(trimmedLine)
                if (infoMatcher.matches()) {
                    val key = infoMatcher.group(1)?.lowercase()
                    val value = infoMatcher.group(2)

                    // 设置歌词信息
                    when (key) {
                        "ti" -> lyricInfo.title = value ?: ""
                        "ar" -> lyricInfo.artist = value ?: ""
                        "al" -> lyricInfo.album = value ?: ""
                        "by" -> lyricInfo.lyricist = value ?: ""
                        "offset" -> {
                            // 处理时间偏移
                        }
                    }
                    continue
                }

                // 尝试解析时间标签
                val timeMatcher = timeTagPattern.matcher(trimmedLine)
                if (timeMatcher.find()) {
                    // 重置匹配器位置
                    timeMatcher.reset()

                    // 优化：预分配容量，减少内存分配
                    val times = ArrayList<Long>(4)
                    var text = trimmedLine

                    // 提取所有时间标签 - 性能优化版本
                    while (timeMatcher.find()) {
                        val min = timeMatcher.group(1)?.toIntOrNull() ?: 0
                        val sec = timeMatcher.group(2)?.toIntOrNull() ?: 0
                        val millis = timeMatcher.group(3)?.let {
                            // 优化：减少条件判断
                            if (it.length == 2) it.toIntOrNull()?.times(10) ?: 0
                            else it.toIntOrNull() ?: 0
                        } ?: 0

                        // 计算总时间（毫秒）- 优化：减少乘法运算
                        val time = (min * 60000 + sec * 1000 + millis).toLong()
                        times.add(time)
                    }

                    // 优化：使用正则表达式一次性移除所有时间标签
                    text = timeTagPattern.matcher(text).replaceAll("").trim()

                    // 处理文本，检查是否有翻译
                    var mainText = text.trim()
                    var translation: String? = null

                    // 检查是否包含翻译分隔符
                    val translationIndex = mainText.indexOf(TRANSLATION_SEPARATOR)
                    if (translationIndex > 0) {
                        translation = mainText.substring(translationIndex).trim()
                        mainText = mainText.substring(0, translationIndex).trim()
                    }

                    // 为每个时间标签创建歌词行
                    for (time in times) {
                        tempEntries.add(LyricLine(time, mainText, translation))
                    }
                }
            }

            // 按时间排序
            val sortedEntries = tempEntries.sortedBy { it.time }

            // 设置歌词条目
            lyricInfo.entries.clear()
            lyricInfo.entries.addAll(sortedEntries)

            return@withContext lyricInfo
        } catch (e: Exception) {
            Log.e(TAG, "解析歌词失败", e)
            return@withContext lyricInfo
        }
    }

    /**
     * 解析带有翻译的歌词
     * @param mainLrc 主歌词内容
     * @param translationLrc 翻译歌词内容
     * @return 解析后的歌词信息
     */
    suspend fun parseWithTranslation(mainLrc: String, translationLrc: String): LyricInfo = withContext(Dispatchers.Default) {
        try {
            // 解析主歌词
            val mainLyricInfo = parseLrc(mainLrc)

            // 解析翻译歌词
            val translationLyricInfo = parseLrc(translationLrc)

            // 合并歌词
            mergeLyrics(mainLyricInfo, translationLyricInfo)
        } catch (e: Exception) {
            Log.e(TAG, "解析带翻译歌词失败", e)
            parseLrc(mainLrc)
        }
    }

    /**
     * 合并主歌词和翻译歌词
     * @param mainLyricInfo 主歌词信息
     * @param translationLyricInfo 翻译歌词信息
     * @return 合并后的歌词信息
     */
    private fun mergeLyrics(mainLyricInfo: LyricInfo, translationLyricInfo: LyricInfo): LyricInfo {
        val result = LyricInfo(
            title = mainLyricInfo.title,
            artist = mainLyricInfo.artist,
            album = mainLyricInfo.album,
            lyricist = mainLyricInfo.lyricist,
            composer = mainLyricInfo.composer,
            entries = mutableListOf(),
            hasTranslation = true
        )

        // 获取主歌词和翻译歌词的条目
        val mainEntries = mainLyricInfo.entries
        val translationEntries = translationLyricInfo.entries

        // 如果翻译歌词为空，直接返回主歌词
        if (translationEntries.isEmpty()) {
            result.entries.addAll(mainEntries)
            return result
        }

        // 合并歌词
        val mergedEntries = mutableListOf<LyricLine>()

        for (mainEntry in mainEntries) {
            // 查找最接近的翻译歌词
            val closestTranslation = findClosestTranslation(mainEntry, translationEntries)

            // 创建新的歌词行
            val mergedLine = LyricLine(
                mainEntry.time,
                mainEntry.text,
                closestTranslation?.text
            )

            mergedEntries.add(mergedLine)
        }

        // 设置合并后的歌词条目
        result.entries.addAll(mergedEntries)

        return result
    }

    /**
     * 查找最接近的翻译歌词
     * @param mainEntry 主歌词行
     * @param translationEntries 翻译歌词条目列表
     * @return 最接近的翻译歌词行
     */
    private fun findClosestTranslation(mainEntry: LyricLine, translationEntries: List<LyricLine>): LyricLine? {
        if (translationEntries.isEmpty()) return null

        // 查找时间最接近的翻译歌词
        var closestEntry: LyricLine? = null
        var minTimeDiff = Long.MAX_VALUE

        for (translationEntry in translationEntries) {
            val timeDiff = Math.abs(translationEntry.time - mainEntry.time)
            if (timeDiff < minTimeDiff) {
                minTimeDiff = timeDiff
                closestEntry = translationEntry
            }
        }

        // 如果时间差太大（超过2秒），认为没有对应的翻译
        return if (minTimeDiff <= 2000) closestEntry else null
    }

    /**
     * 将歌词信息转换为LRC格式字符串
     * @param lyricInfo 歌词信息
     * @return LRC格式字符串
     */
    fun toLrcString(lyricInfo: LyricInfo): String {
        val sb = StringBuilder()

        // 添加歌词信息
        val title = lyricInfo.title
        val artist = lyricInfo.artist
        val album = lyricInfo.album
        val lyricist = lyricInfo.lyricist

        if (title.isNotEmpty()) sb.appendLine("[ti:$title]")
        if (artist.isNotEmpty()) sb.appendLine("[ar:$artist]")
        if (album.isNotEmpty()) sb.appendLine("[al:$album]")
        if (lyricist.isNotEmpty()) sb.appendLine("[by:$lyricist]")

        // 添加歌词条目
        for (entry in lyricInfo.entries) {
            val timeTag = formatTimeTag(entry.time)

            if (entry.translation != null) {
                sb.appendLine("$timeTag${entry.text} ${TRANSLATION_SEPARATOR}${entry.translation}")
            } else {
                sb.appendLine("$timeTag${entry.text}")
            }
        }

        return sb.toString()
    }

    /**
     * 格式化时间标签
     * @param timeMs 时间（毫秒）
     * @return 格式化的时间标签，如[00:12.34]
     */
    private fun formatTimeTag(timeMs: Long): String {
        val totalSeconds = timeMs / 1000
        val minutes = totalSeconds / 60
        val seconds = totalSeconds % 60
        val milliseconds = timeMs % 1000 / 10

        return String.format("[%02d:%02d.%02d]", minutes, seconds, milliseconds)
    }
}
