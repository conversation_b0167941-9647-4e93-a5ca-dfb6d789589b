<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 背景图层 -->
    <ImageView
        android:id="@+id/background_blur"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:contentDescription="背景"
        android:alpha="0.8" />

    <!-- 半透明遮罩层，增加对比度 -->
    <View
        android:id="@+id/background_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_black"
        android:alpha="0.6" />

    <!-- 主要内容区域 - 歌曲信息在左侧，歌词/评论/播放列表在右侧 -->
    <LinearLayout
        android:id="@+id/content_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/control_container"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 左侧专辑和歌曲信息区域 -->
        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:gravity="center_vertical">

            <!-- 黑胶唱片和专辑封面 - 使用自定义AlbumCoverView，整体下移更多避免过于靠上 -->
            <com.example.aimusicplayer.ui.widget.AlbumCoverView
                android:id="@+id/album_cover_view"
                android:layout_width="450dp"
                android:layout_height="450dp"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="80dp" />

            <!-- 保留原有的ImageView作为备用 -->
            <ImageView
                android:id="@+id/album_art"
                android:layout_width="280dp"
                android:layout_height="280dp"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="130dp"
                android:scaleType="centerCrop"
                android:background="@drawable/album_art_border"
                android:contentDescription="专辑封面"
                android:elevation="2dp"
                android:visibility="gone"
                tools:src="@drawable/default_album_art" />

            <!-- 黑胶唱片底盘 - 保留用于旋转动画 -->
            <ImageView
                android:id="@+id/vinyl_background"
                android:layout_width="420dp"
                android:layout_height="420dp"
                android:layout_centerHorizontal="true"
                android:src="@drawable/vinyl_record"
                android:contentDescription="黑胶唱片"
                android:elevation="1dp"
                android:visibility="gone" />

            <!-- 歌曲和艺术家信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/album_cover_view"
                android:layout_marginTop="20dp"
                android:orientation="vertical"
                android:gravity="center">

                <TextView
                    android:id="@+id/song_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/text_light"
                    android:textSize="34sp"
                    android:textStyle="bold"
                    android:shadowColor="#80000000"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="3"
                    tools:text="Love Me Not" />

                <TextView
                    android:id="@+id/song_artist"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/color_gray_300"
                    android:textSize="24sp"
                    android:shadowColor="#80000000"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="2"
                    tools:text="Ravyn Lenae" />
            </LinearLayout>
        </RelativeLayout>

        <!-- 右侧内容区域 - 使用ViewPager2实现歌词、评论和播放列表的切换 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="3"
            android:layout_marginStart="16dp"
            android:orientation="vertical">

            <!-- 内容标签页 -->
            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tab_layout_player"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@android:color/transparent"
                app:tabIndicatorColor="?colorAccent"
                app:tabSelectedTextColor="?colorAccent"
                app:tabTextColor="@color/color_gray_500"
                app:tabMode="fixed"
                app:tabGravity="center"
                android:visibility="gone" />

            <!-- 内容区域 - 直接使用ViewPager2，移除CardView -->
            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/view_pager_player"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="20dp"
                android:layout_marginVertical="10dp" />
        </LinearLayout>
    </LinearLayout>

    <!-- 加载动画 -->
    <com.example.aimusicplayer.ui.widget.LottieLoadingView
        android:id="@+id/loading_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone"
        android:elevation="10dp"
        app:lottieAnimationAsset="music_loading.json"
        app:loadingMessage="加载中..."
        app:autoPlay="true"
        app:loop="true" />

    <!-- 底部控制区域 -->
    <LinearLayout
        android:id="@+id/control_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical"
        android:background="@color/color_black"
        android:alpha="0.1"
        android:padding="20dp"
        android:layout_marginHorizontal="10dp"
        android:layout_marginBottom="10dp">

        <!-- 进度条和时间 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="20dp"
            android:layout_marginHorizontal="10dp">

            <TextView
                android:id="@+id/textview_player_current_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_light"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginEnd="10dp"
                android:text="00:00" />

            <SeekBar
                android:id="@+id/seekbar_player_progress"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:progressTint="@color/theme_accent"
                android:thumbTint="@color/theme_accent"
                android:minHeight="8dp"
                android:maxHeight="8dp"
                android:layout_gravity="center_vertical"
                android:thumbOffset="8dp" />

            <TextView
                android:id="@+id/textview_player_total_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_light"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginStart="10dp"
                android:text="00:00" />
        </LinearLayout>

        <!-- 播放控制按钮 - 单行布局，按用户要求的顺序排列 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginHorizontal="20dp">

            <!-- 歌曲列表按钮 -->
            <ImageView
                android:id="@+id/button_player_playlist"
                android:layout_width="0dp"
                android:layout_height="60dp"
                android:layout_weight="1"
                android:padding="12dp"
                android:src="@drawable/ic_playlist"
                android:contentDescription="歌曲列表"
                android:clickable="true"
                android:focusable="true"
                android:background="@drawable/ripple_oval_button" />

            <!-- 播放模式按钮 -->
            <ImageView
                android:id="@+id/button_player_play_mode"
                android:layout_width="0dp"
                android:layout_height="60dp"
                android:layout_weight="1"
                android:padding="12dp"
                android:src="@drawable/ic_repeat"
                android:contentDescription="播放模式"
                android:clickable="true"
                android:focusable="true"
                android:background="@drawable/ripple_oval_button" />

            <!-- 上一首按钮 -->
            <ImageView
                android:id="@+id/button_player_prev"
                android:layout_width="0dp"
                android:layout_height="70dp"
                android:layout_weight="1"
                android:padding="15dp"
                android:src="@drawable/ic_previous"
                android:contentDescription="上一首"
                android:clickable="true"
                android:focusable="true"
                android:background="@drawable/ripple_oval_button" />

            <!-- 播放/暂停按钮 - 不拉长，正常显示 -->
            <ImageView
                android:id="@+id/button_player_play_pause"
                android:layout_width="0dp"
                android:layout_height="70dp"
                android:layout_weight="1"
                android:padding="15dp"
                android:src="@drawable/ic_playing_play_pause_selector"
                android:contentDescription="播放/暂停"
                android:clickable="true"
                android:focusable="true"
                android:background="@drawable/ripple_circular_button" />

            <!-- 下一首按钮 -->
            <ImageView
                android:id="@+id/button_player_next"
                android:layout_width="0dp"
                android:layout_height="70dp"
                android:layout_weight="1"
                android:padding="15dp"
                android:src="@drawable/ic_next"
                android:contentDescription="下一首"
                android:clickable="true"
                android:focusable="true"
                android:background="@drawable/ripple_oval_button" />

            <!-- 评论按钮 -->
            <ImageView
                android:id="@+id/button_player_comment"
                android:layout_width="0dp"
                android:layout_height="60dp"
                android:layout_weight="1"
                android:padding="12dp"
                android:src="@drawable/ic_comment"
                android:contentDescription="评论"
                android:clickable="true"
                android:focusable="true"
                android:background="@drawable/ripple_oval_button" />

            <!-- 收藏按钮 -->
            <ImageView
                android:id="@+id/button_player_collect"
                android:layout_width="0dp"
                android:layout_height="60dp"
                android:layout_weight="1"
                android:padding="12dp"
                android:src="@drawable/ic_favorite_border"
                android:contentDescription="收藏"
                android:clickable="true"
                android:focusable="true"
                android:background="@drawable/ripple_oval_button" />
        </LinearLayout>
    </LinearLayout>
</RelativeLayout>