<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_player" modulePackage="com.example.aimusicplayer" filePath="app\src\main\res\layout\fragment_player.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/fragment_player_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="315" endOffset="16"/></Target><Target id="@+id/background_blur" view="ImageView"><Expressions/><location startLine="8" startOffset="4" endLine="14" endOffset="29"/></Target><Target id="@+id/background_overlay" view="View"><Expressions/><location startLine="17" startOffset="4" endLine="22" endOffset="29"/></Target><Target id="@+id/content_container" view="LinearLayout"><Expressions/><location startLine="25" startOffset="4" endLine="146" endOffset="18"/></Target><Target id="@+id/album_cover_view" view="com.example.aimusicplayer.ui.widget.AlbumCoverView"><Expressions/><location startLine="41" startOffset="12" endLine="46" endOffset="49"/></Target><Target id="@+id/album_art" view="ImageView"><Expressions/><location startLine="49" startOffset="12" endLine="60" endOffset="57"/></Target><Target id="@+id/vinyl_background" view="ImageView"><Expressions/><location startLine="63" startOffset="12" endLine="71" endOffset="43"/></Target><Target id="@+id/song_title" view="TextView"><Expressions/><location startLine="83" startOffset="16" endLine="97" endOffset="46"/></Target><Target id="@+id/song_artist" view="TextView"><Expressions/><location startLine="99" startOffset="16" endLine="113" endOffset="46"/></Target><Target id="@+id/tab_layout_player" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="126" startOffset="12" endLine="136" endOffset="43"/></Target><Target id="@+id/view_pager_player" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="139" startOffset="12" endLine="144" endOffset="54"/></Target><Target id="@+id/loading_view" view="com.example.aimusicplayer.ui.widget.LottieLoadingView"><Expressions/><location startLine="149" startOffset="4" endLine="159" endOffset="25"/></Target><Target id="@+id/control_container" view="LinearLayout"><Expressions/><location startLine="162" startOffset="4" endLine="314" endOffset="18"/></Target><Target id="@+id/textview_player_current_time" view="TextView"><Expressions/><location startLine="182" startOffset="12" endLine="190" endOffset="38"/></Target><Target id="@+id/seekbar_player_progress" view="SeekBar"><Expressions/><location startLine="192" startOffset="12" endLine="202" endOffset="43"/></Target><Target id="@+id/textview_player_total_time" view="TextView"><Expressions/><location startLine="204" startOffset="12" endLine="212" endOffset="38"/></Target><Target id="@+id/button_player_playlist" view="ImageView"><Expressions/><location startLine="224" startOffset="12" endLine="234" endOffset="70"/></Target><Target id="@+id/button_player_play_mode" view="ImageView"><Expressions/><location startLine="237" startOffset="12" endLine="247" endOffset="70"/></Target><Target id="@+id/button_player_prev" view="ImageView"><Expressions/><location startLine="250" startOffset="12" endLine="260" endOffset="70"/></Target><Target id="@+id/button_player_play_pause" view="ImageView"><Expressions/><location startLine="263" startOffset="12" endLine="273" endOffset="71"/></Target><Target id="@+id/button_player_next" view="ImageView"><Expressions/><location startLine="276" startOffset="12" endLine="286" endOffset="70"/></Target><Target id="@+id/button_player_comment" view="ImageView"><Expressions/><location startLine="289" startOffset="12" endLine="299" endOffset="70"/></Target><Target id="@+id/button_player_collect" view="ImageView"><Expressions/><location startLine="302" startOffset="12" endLine="312" endOffset="70"/></Target></Targets></Layout>