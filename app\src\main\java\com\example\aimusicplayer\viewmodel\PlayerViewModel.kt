package com.example.aimusicplayer.viewmodel

import android.app.Application
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Bitmap
import android.net.Uri
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.asLiveData
import androidx.lifecycle.viewModelScope
import androidx.media3.common.MediaItem
import com.example.aimusicplayer.data.repository.MusicRepository
import com.example.aimusicplayer.error.GlobalErrorHandler
import com.example.aimusicplayer.data.model.LyricLine
import com.example.aimusicplayer.data.model.LyricInfo
import com.example.aimusicplayer.data.model.Song
import com.example.aimusicplayer.service.PlayMode
import com.example.aimusicplayer.service.PlayState
import com.example.aimusicplayer.service.PlayerController
import com.example.aimusicplayer.utils.CacheManager
import com.example.aimusicplayer.utils.ImageUtils
import com.example.aimusicplayer.utils.LyricCache
import com.example.aimusicplayer.utils.EnhancedLyricParser
import com.example.aimusicplayer.utils.LyricParser
import com.example.aimusicplayer.utils.PlaylistCache
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

/**
 * 播放器的ViewModel
 * 负责处理播放器的业务逻辑，与PlayerController交互
 * 使用Flow管理UI状态
 */
@HiltViewModel
class PlayerViewModel @Inject constructor(
    application: Application,
    private val playerController: PlayerController,
    private val musicRepository: MusicRepository,
    errorHandler: GlobalErrorHandler
) : FlowViewModel(application) {

    // 广播接收器，用于接收新歌速递加载请求
    private val newSongsReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (intent.action == "com.example.aimusicplayer.LOAD_NEW_SONGS") {
                Log.d(TAG, "收到新歌速递加载广播")
                loadNewSongs()
            }
        }
    }

    init {
        // 设置错误处理器
        this.errorHandler = errorHandler

        // 注册广播接收器
        val filter = IntentFilter("com.example.aimusicplayer.LOAD_NEW_SONGS")
        application.registerReceiver(newSongsReceiver, filter)
    }

    private val TAG = "PlayerViewModel"

    // 当前歌曲
    val currentSong: LiveData<MediaItem?> = playerController.currentSong

    // 播放状态 - 使用Flow
    private val _playStateFlow = MutableStateFlow<PlayState>(PlayState.Idle)
    val playStateFlow: StateFlow<PlayState> = _playStateFlow.asStateFlow()
    val playState: LiveData<PlayState> = playStateFlow.asLiveData() // 兼容LiveData

    // 播放进度 - 使用Flow
    private val _playProgressFlow = MutableStateFlow<Long>(0L)
    val playProgressFlow: StateFlow<Long> = _playProgressFlow.asStateFlow()
    val playProgress: LiveData<Long> = playProgressFlow.asLiveData() // 兼容LiveData

    // 当前播放位置 - 使用LiveData
    private val _currentPosition = MutableLiveData<Long>(0L)
    val currentPosition: LiveData<Long> = _currentPosition

    // 缓冲百分比 - 使用Flow
    private val _bufferingPercentFlow = MutableStateFlow<Int>(0)
    val bufferingPercentFlow: StateFlow<Int> = _bufferingPercentFlow.asStateFlow()
    val bufferingPercent: LiveData<Int> = bufferingPercentFlow.asLiveData() // 兼容LiveData

    // 播放模式 - 使用Flow
    private val _playModeFlow = MutableStateFlow<PlayMode>(PlayMode.Loop)
    val playModeFlow: StateFlow<PlayMode> = _playModeFlow.asStateFlow()
    val playMode: LiveData<PlayMode> = playModeFlow.asLiveData() // 兼容LiveData

    // 歌曲总时长 - 使用Flow
    private val _durationFlow = MutableStateFlow<Long>(0L)
    val durationFlow: StateFlow<Long> = _durationFlow.asStateFlow()
    val duration: LiveData<Long> = durationFlow.asLiveData() // 兼容LiveData

    // 歌词 - 使用Flow
    private val _lyricFlow = MutableStateFlow<String>("")
    val lyricFlow: StateFlow<String> = _lyricFlow.asStateFlow()
    val lyric: LiveData<String> = lyricFlow.asLiveData() // 兼容LiveData

    // 歌词信息 - 使用Flow
    private val _lyricInfoFlow = MutableStateFlow<com.example.aimusicplayer.data.model.LyricInfo?>(null)
    val lyricInfoFlow: StateFlow<com.example.aimusicplayer.data.model.LyricInfo?> = _lyricInfoFlow.asStateFlow()
    val lyricInfo: LiveData<com.example.aimusicplayer.data.model.LyricInfo?> = lyricInfoFlow.asLiveData() // 兼容LiveData

    // 歌词信息 - 使用LiveData (兼容旧代码)
    private val _lyrics = MutableLiveData<com.example.aimusicplayer.data.model.LyricInfo?>()
    val lyrics: LiveData<com.example.aimusicplayer.data.model.LyricInfo?> = _lyrics

    // 专辑封面 - 使用Flow
    private val _albumCoverFlow = MutableStateFlow<Bitmap?>(null)
    val albumCoverFlow: StateFlow<Bitmap?> = _albumCoverFlow.asStateFlow()
    val albumCover: LiveData<Bitmap?> = albumCoverFlow.asLiveData() // 兼容LiveData

    // 模糊背景 - 使用Flow
    private val _blurredBackgroundFlow = MutableStateFlow<Bitmap?>(null)
    val blurredBackgroundFlow: StateFlow<Bitmap?> = _blurredBackgroundFlow.asStateFlow()
    val blurredBackground: LiveData<Bitmap?> = blurredBackgroundFlow.asLiveData() // 兼容LiveData

    // 背景颜色 - 使用Flow
    private val _backgroundColorFlow = MutableStateFlow<Int>(0xFF333333.toInt())
    val backgroundColorFlow: StateFlow<Int> = _backgroundColorFlow.asStateFlow()
    val backgroundColor: LiveData<Int> = backgroundColorFlow.asLiveData() // 兼容LiveData

    // 错误信息已经在FlowViewModel中定义，这里直接使用
    override val errorMessage: LiveData<String> = errorMessageFlow.asLiveData() // 兼容LiveData

    // 加载状态已经在FlowViewModel中定义，这里直接使用
    override val loading: LiveData<Boolean> = loadingFlow.asLiveData() // 兼容LiveData

    // 收藏状态 - 使用Flow
    private val _isLikedFlow = MutableStateFlow<Boolean>(false)
    val isLikedFlow: StateFlow<Boolean> = _isLikedFlow.asStateFlow()
    val isLiked: LiveData<Boolean> = isLikedFlow.asLiveData() // 兼容LiveData

    // 当前歌曲收藏状态 - 使用LiveData
    private val _isCurrentSongCollected = MutableLiveData<Boolean>(false)
    val isCurrentSongCollected: LiveData<Boolean> = _isCurrentSongCollected

    // 评论列表 - 使用Flow
    private val _commentsFlow = MutableStateFlow<List<Any>>(emptyList())
    val commentsFlow: StateFlow<List<Any>> = _commentsFlow.asStateFlow()
    val comments: LiveData<List<Any>> = commentsFlow.asLiveData() // 兼容LiveData

    // 热门评论列表 - 使用Flow
    private val _hotCommentsFlow = MutableStateFlow<List<Any>>(emptyList())
    val hotCommentsFlow: StateFlow<List<Any>> = _hotCommentsFlow.asStateFlow()
    val hotComments: LiveData<List<Any>> = hotCommentsFlow.asLiveData() // 兼容LiveData

    // 评论总数 - 使用Flow
    private val _commentCountFlow = MutableStateFlow<Int>(0)
    val commentCountFlow: StateFlow<Int> = _commentCountFlow.asStateFlow()
    val commentCount: LiveData<Int> = commentCountFlow.asLiveData() // 兼容LiveData

    // 相似歌曲列表 - 使用Flow
    private val _similarSongsFlow = MutableStateFlow<List<MediaItem>>(emptyList())
    val similarSongsFlow: StateFlow<List<MediaItem>> = _similarSongsFlow.asStateFlow()
    val similarSongs: LiveData<List<MediaItem>> = similarSongsFlow.asLiveData() // 兼容LiveData

    // 播放队列 - 使用Flow
    private val _playQueueFlow = MutableStateFlow<List<MediaItem>>(emptyList())
    val playQueueFlow: StateFlow<List<MediaItem>> = _playQueueFlow.asStateFlow()
    val playQueue: LiveData<List<MediaItem>> = playQueueFlow.asLiveData() // 兼容LiveData

    // 歌词缓存
    private val lyricCache = LyricCache(application.cacheDir)

    // 播放列表缓存
    private val playlistCache = PlaylistCache(application)

    // 当前播放列表ID
    private var currentPlaylistId = "default"

    init {
        // 监听播放状态
        viewModelScope.launch {
            playerController.playState.collectLatest {
                _playStateFlow.value = it
            }
        }

        // 监听播放进度
        viewModelScope.launch {
            playerController.playProgress.collectLatest {
                _playProgressFlow.value = it
                _currentPosition.postValue(it) // 更新当前播放位置

                // 计算总时长
                val currentSong = playerController.currentSong.value
                if (currentSong != null) {
                    val durationMs = currentSong.mediaMetadata.extras?.getLong("duration") ?: 0
                    if (durationMs > 0) {
                        _durationFlow.value = durationMs
                    }
                }
            }
        }

        // 监听缓冲百分比
        viewModelScope.launch {
            playerController.bufferingPercent.collectLatest {
                _bufferingPercentFlow.value = it
            }
        }

        // 监听播放模式
        viewModelScope.launch {
            playerController.playMode.collectLatest {
                _playModeFlow.value = it
            }
        }

        // 监听播放队列
        viewModelScope.launch {
            playerController.playlist.observeForever { mediaItems ->
                _playQueueFlow.value = mediaItems
            }
        }

        // 监听当前歌曲变化
        viewModelScope.launch {
            playerController.currentSong.observeForever { mediaItem ->
                mediaItem?.let {
                    // 加载歌词
                    val songId = it.mediaId.toLongOrNull()
                    if (songId != null) {
                        loadLyricInfo(songId)

                        // 检查收藏状态
                        checkLikeStatus(songId)
                    }

                    // 加载专辑封面
                    val artworkUri = it.mediaMetadata.artworkUri
                    if (artworkUri != null) {
                        loadAlbumCover(artworkUri)
                    }

                    // 保存当前播放列表
                    saveCurrentPlaylist()
                }
            }
        }

        // 加载缓存的播放列表
        loadCachedPlaylist()

        // 清理过期缓存
        cleanExpiredCaches()
    }

    /**
     * 清理过期缓存
     */
    private fun cleanExpiredCaches() {
        viewModelScope.launch {
            try {
                // 清理歌词缓存
                lyricCache.cleanExpiredCache()

                // 清理其他缓存
                CacheManager.getInstance(getApplication()).checkAndCleanCache()

                Log.d(TAG, "缓存清理完成")
            } catch (e: Exception) {
                Log.e(TAG, "缓存清理失败", e)
            }
        }
    }

    /**
     * 播放/暂停
     */
    fun playPause() {
        playerController.playPause()
    }

    /**
     * 切换播放/暂停状态
     */
    fun togglePlayPause() {
        playerController.playPause()
    }

    /**
     * 播放上一首
     */
    fun playPrevious() {
        playerController.prev()
    }

    /**
     * 跳转到上一首
     */
    fun skipToPrevious() {
        playerController.prev()
    }

    /**
     * 播放下一首
     */
    fun playNext() {
        playerController.next()
    }

    /**
     * 跳转到下一首
     */
    fun skipToNext() {
        playerController.next()
    }

    /**
     * 切换播放模式
     */
    fun switchPlayMode() {
        val currentMode = playMode.value ?: PlayMode.Loop
        val newMode = when (currentMode) {
            PlayMode.Loop -> PlayMode.Shuffle
            PlayMode.Shuffle -> PlayMode.Single
            PlayMode.Single -> PlayMode.Loop
        }
        playerController.setPlayMode(newMode)
    }

    /**
     * 切换播放模式
     */
    fun togglePlayMode() {
        switchPlayMode()
    }

    /**
     * 跳转到指定位置
     */
    fun seekTo(position: Int) {
        playerController.seekTo(position)
    }

    /**
     * 加载歌词信息
     */
    fun loadLyricInfo(songId: Long) {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载歌词失败", e)
                handleError(e, "加载歌词失败: ${e.message}")
                _lyricFlow.value = ""
                _lyricInfoFlow.value = null // Changed to _lyricInfoFlow and set to null
            }
        ) {
            setLoading(true)
            try {
                // 先从缓存获取
                var lyricInfo = lyricCache.getLyric(songId)

                if (lyricInfo == null) {
                    // 缓存中没有，从网络获取
                    val lyricResponse = musicRepository.getLyric(songId)

                    if (lyricResponse != null) {
                        // 使用增强型歌词解析器处理
                        val lrcContent = lyricResponse.lrc?.lyric ?: ""
                        val translationContent = lyricResponse.tlyric?.lyric ?: ""

                        // 解析歌词
                        val javaLyricInfo = if (translationContent.isNotEmpty()) {
                            EnhancedLyricParser.parseWithTranslation(lrcContent, translationContent)
                        } else {
                            EnhancedLyricParser.parseLrc(lrcContent)
                        }

                        // 直接使用解析后的歌词信息
                        _lyricInfoFlow.value = javaLyricInfo
                        _lyricFlow.value = EnhancedLyricParser.toLrcString(javaLyricInfo)

                        // 缓存歌词
                        lyricCache.saveLyric(songId, javaLyricInfo)

                        return@launchSafely
                    }
                }

                // 如果从缓存获取到了歌词
                if (lyricInfo != null) {
                    // 使用增强型歌词解析器解析
                    val javaLyricInfo = EnhancedLyricParser.parseLrc(lyricInfo.toString())

                    // 更新UI
                    _lyricInfoFlow.value = javaLyricInfo
                    _lyricFlow.value = lyricInfo.toString()
                } else {
                    // 没有歌词
                    _lyricFlow.value = ""
                    _lyricInfoFlow.value = null // Changed to _lyricInfoFlow and set to null
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 获取歌曲详情 - 调用API获取完整信息包括高质量专辑封面
     */
    suspend fun getSongDetail(songId: Long): Song? {
        return try {
            musicRepository.getSongDetail(songId)
        } catch (e: Exception) {
            Log.e(TAG, "获取歌曲详情失败", e)
            null
        }
    }

    /**
     * 加载专辑封面
     */
    fun loadAlbumCover(artworkUri: Uri) {
        viewModelScope.launch {
            try {
                val context = getApplication<Application>()
                val cacheKey = "album_${artworkUri}"

                // 加载并处理专辑封面
                val albumCover = ImageUtils.loadAndProcessAlbumCover(context, artworkUri, cacheKey)
                if (albumCover != null) {
                    _albumCoverFlow.value = albumCover

                    // 创建模糊背景
                    val blurCacheKey = "blur_${artworkUri}"
                    val blurredBitmap = ImageUtils.loadAndCreateBlurredBackground(context, artworkUri, blurCacheKey, 25f)
                    if (blurredBitmap != null) {
                        _blurredBackgroundFlow.value = blurredBitmap
                    }

                    // 提取背景颜色
                    val color = ImageUtils.extractDominantColorWithCache(context, artworkUri, 0xFF333333.toInt())
                    _backgroundColorFlow.value = color
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载专辑封面失败", e)
            }
        }
    }

    // 错误弹窗显示状态
    private val _showErrorDialog = MutableLiveData<String>()
    val showErrorDialog: LiveData<String> = _showErrorDialog

    /**
     * 加载新歌速递并播放
     */
    fun loadNewSongs() {
        launchSafely(
            onError = { e ->
                Log.e(TAG, "加载新歌速递失败", e)
                // 显示错误弹窗而不是Toast
                _showErrorDialog.value = "获取新歌速递失败，请检查网络连接后重试"
            }
        ) {
            setLoading(true)
            try {
                val songs = musicRepository.getNewSongsAsMediaItems()
                if (songs.isNotEmpty()) {
                    // 随机选择一首歌曲播放
                    val randomIndex = (0 until songs.size).random()
                    val song = songs[randomIndex]

                    // 播放歌曲
                    playerController.replaceAll(songs, song)

                    // 更新当前播放列表ID
                    currentPlaylistId = "new_songs"

                    // 保存播放列表
                    saveCurrentPlaylist()
                } else {
                    _showErrorDialog.value = "新歌速递列表为空，请稍后重试"
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 保存当前播放列表
     */
    private fun saveCurrentPlaylist() {
        viewModelScope.launch {
            try {
                val mediaItems = playerController.getCurrentPlaylist()
                val currentIndex = playerController.getCurrentIndex()

                if (mediaItems.isNotEmpty()) {
                    playlistCache.savePlaylist(currentPlaylistId, mediaItems, currentIndex)
                    Log.d(TAG, "保存播放列表成功: $currentPlaylistId")
                }
            } catch (e: Exception) {
                Log.e(TAG, "保存播放列表失败", e)
            }
        }
    }

    /**
     * 加载缓存的播放列表
     */
    private fun loadCachedPlaylist() {
        viewModelScope.launch {
            try {
                val playlistInfo = playlistCache.loadPlaylist(currentPlaylistId)
                if (playlistInfo != null && playlistInfo.mediaItems.isNotEmpty()) {
                    // 恢复播放列表
                    playerController.replaceAll(
                        playlistInfo.mediaItems,
                        playlistInfo.mediaItems[playlistInfo.currentIndex]
                    )
                    Log.d(TAG, "加载缓存的播放列表成功: $currentPlaylistId")
                } else {
                    // 没有缓存的播放列表，加载新歌速递
                    loadNewSongs()
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载缓存的播放列表失败", e)
                // 加载失败，加载新歌速递
                loadNewSongs()
            }
        }
    }

    /**
     * 根据歌词时间跳转到对应位置
     */
    fun seekToLyricTime(time: Long) {
        playerController.seekTo(time.toInt())
    }

    /**
     * 获取音频会话ID
     */
    fun getAudioSessionId(): Int {
        return playerController.getAudioSessionId()
    }

    /**
     * 检查歌曲收藏状态
     */
    private fun checkLikeStatus(songId: Long) {
        viewModelScope.launch {
            try {
                val isLiked = musicRepository.checkLikeStatus(songId)
                _isLikedFlow.value = isLiked
                _isCurrentSongCollected.postValue(isLiked) // 更新当前歌曲收藏状态
            } catch (e: Exception) {
                Log.e(TAG, "检查收藏状态失败", e)
                _isLikedFlow.value = false
                _isCurrentSongCollected.postValue(false) // 更新当前歌曲收藏状态
            }
        }
    }

    /**
     * 切换收藏状态
     */
    fun toggleLike() {
        val currentSong = currentSong.value ?: return
        val songId = currentSong.mediaId.toLongOrNull() ?: return
        val isCurrentlyLiked = _isLikedFlow.value

        launchSafely(
            onError = { e ->
                Log.e(TAG, "切换收藏状态失败", e)
                handleError(e, "切换收藏状态失败: ${e.message}")
            }
        ) {
            setLoading(true)
            try {
                if (isCurrentlyLiked) {
                    // 取消收藏
                    val success = musicRepository.unlikeSong(songId)
                    if (success) {
                        _isLikedFlow.value = false
                        _isCurrentSongCollected.postValue(false) // 更新当前歌曲收藏状态
                    }
                } else {
                    // 添加收藏
                    val success = musicRepository.likeSong(songId)
                    if (success) {
                        _isLikedFlow.value = true
                        _isCurrentSongCollected.postValue(true) // 更新当前歌曲收藏状态
                    }
                }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 切换收藏状态
     */
    fun toggleCollect() {
        toggleLike()
    }

    /**
     * 播放指定索引的歌曲
     */
    fun playAtIndex(index: Int) {
        val playlist = playQueue.value ?: return
        if (index >= 0 && index < playlist.size) {
            val song = playlist[index]
            playerController.play(song.mediaId)
        }
    }

    /**
     * 从播放列表中移除歌曲
     */
    fun removeFromPlaylist(index: Int) {
        val currentPlaylist = playQueue.value?.toMutableList() ?: return
        if (index >= 0 && index < currentPlaylist.size) {
            currentPlaylist.removeAt(index)
            // 更新播放列表
            if (currentPlaylist.isNotEmpty()) {
                playerController.replaceAll(currentPlaylist, currentPlaylist[0])
            } else {
                playerController.stop()
            }
        }
    }

    /**
     * 清空播放列表
     */
    fun clearPlaylist() {
        playerController.stop()
        _playQueueFlow.value = emptyList()
    }

    /**
     * 随机播放列表
     */
    fun shufflePlaylist() {
        val currentPlaylist = playQueue.value?.toMutableList() ?: return
        if (currentPlaylist.size > 1) {
            currentPlaylist.shuffle()
            playerController.replaceAll(currentPlaylist, currentPlaylist[0])
        }
    }

    // 评论分页参数
    private var commentPage = 1
    private val commentPageSize = 20
    private var hasMoreComments = true

    /**
     * 加载评论
     */
    fun loadComments() {
        val currentSong = currentSong.value ?: return
        val songId = currentSong.mediaId.toLongOrNull() ?: return

        // 重置分页参数
        commentPage = 1
        hasMoreComments = true

        viewModelScope.launch {
            try {
                setLoading(true)

                // 加载热门评论
                val hotComments = musicRepository.getHotComments(songId)
                _hotCommentsFlow.value = hotComments

                // 加载普通评论（第一页）
                val offset = 0
                val comments = musicRepository.getComments(songId, offset, commentPageSize)
                _commentsFlow.value = comments

                // 更新评论总数
                _commentCountFlow.value = hotComments.size + comments.size

                // 检查是否还有更多评论
                hasMoreComments = comments.size >= commentPageSize
            } catch (e: Exception) {
                Log.e(TAG, "加载评论失败", e)
                viewModelScope.launch { _errorMessageFlow.emit("加载评论失败: ${e.message}") }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 刷新评论
     */
    fun refreshComments() {
        val currentSong = currentSong.value ?: return
        val songId = currentSong.mediaId.toLongOrNull() ?: return

        // 重置分页参数
        commentPage = 1
        hasMoreComments = true

        viewModelScope.launch {
            try {
                setLoading(true)

                // 强制刷新热门评论
                val hotComments = musicRepository.getHotComments(songId, forceRefresh = true)
                _hotCommentsFlow.value = hotComments

                // 强制刷新普通评论（第一页）
                val offset = 0
                val comments = musicRepository.getComments(songId, offset, commentPageSize)
                _commentsFlow.value = comments

                // 更新评论总数
                _commentCountFlow.value = hotComments.size + comments.size

                // 检查是否还有更多评论
                hasMoreComments = comments.size >= commentPageSize
            } catch (e: Exception) {
                Log.e(TAG, "刷新评论失败", e)
                viewModelScope.launch { _errorMessageFlow.emit("刷新评论失败: ${e.message}") }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 加载更多评论
     */
    fun loadMoreComments() {
        val currentSong = currentSong.value ?: return
        val songId = currentSong.mediaId.toLongOrNull() ?: return

        // 如果没有更多评论，直接返回
        if (!hasMoreComments) {
            viewModelScope.launch { _errorMessageFlow.emit("没有更多评论了") }
            return
        }

        viewModelScope.launch {
            try {
                setLoading(true)

                // 页码加1
                commentPage++

                // 计算偏移量并加载下一页评论
                val offset = (commentPage - 1) * commentPageSize
                val newComments = musicRepository.getComments(songId, offset, commentPageSize)

                // 合并评论列表
                val currentComments = _commentsFlow.value.toMutableList()
                currentComments.addAll(newComments)
                _commentsFlow.value = currentComments

                // 更新评论总数
                _commentCountFlow.value = _hotCommentsFlow.value.size + currentComments.size

                // 检查是否还有更多评论
                hasMoreComments = newComments.size >= commentPageSize

                if (newComments.isEmpty()) {
                    viewModelScope.launch { _errorMessageFlow.emit("没有更多评论了") }
                }
            } catch (e: Exception) {
                Log.e(TAG, "加载更多评论失败", e)
                viewModelScope.launch { _errorMessageFlow.emit("加载更多评论失败: ${e.message}") }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 发送评论
     */
    fun sendComment(content: String) {
        val currentSong = currentSong.value ?: return
        val songId = currentSong.mediaId.toLongOrNull() ?: return

        if (content.isBlank()) {
            viewModelScope.launch { _errorMessageFlow.emit("评论内容不能为空") }
            return
        }

        viewModelScope.launch {
            try {
                setLoading(true)

                // 发送评论
                val success = musicRepository.sendComment(songId, content)

                if (success) {
                    // 重新加载评论
                    loadComments()
                    viewModelScope.launch { _errorMessageFlow.emit("评论发送成功") }
                } else {
                    viewModelScope.launch { _errorMessageFlow.emit("评论发送失败") }
                }
            } catch (e: Exception) {
                Log.e(TAG, "发送评论失败", e)
                viewModelScope.launch { _errorMessageFlow.emit("发送评论失败: ${e.message}") }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 加载相似歌曲
     * @param songId 歌曲ID
     * @param forceRefresh 是否强制刷新
     */
    fun loadSimilarSongs(songId: Long, forceRefresh: Boolean = false) {
        viewModelScope.launch {
            try {
                setLoading(true)

                // 加载相似歌曲，使用缓存机制
                val similarSongs = musicRepository.getSimilarSongsAsMediaItems(songId, forceRefresh = forceRefresh)
                _similarSongsFlow.value = similarSongs
            } catch (e: Exception) {
                Log.e(TAG, "加载相似歌曲失败", e)
                viewModelScope.launch { _errorMessageFlow.emit("加载相似歌曲失败: ${e.message}") }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 刷新相似歌曲
     */
    fun refreshSimilarSongs(songId: Long) {
        loadSimilarSongs(songId, forceRefresh = true)
    }

    /**
     * 开始心动模式
     */
    fun startHeartMode(songId: Long) {
        viewModelScope.launch {
            try {
                setLoading(true)

                // 加载相似歌曲
                val similarSongs = musicRepository.getSimilarSongsAsMediaItems(songId)

                if (similarSongs.isNotEmpty()) {
                    // 播放相似歌曲
                    playerController.replaceAll(similarSongs, similarSongs[0])

                    // 更新当前播放列表ID
                    currentPlaylistId = "heart_mode_$songId"

                    // 保存播放列表
                    saveCurrentPlaylist()

                    viewModelScope.launch { _errorMessageFlow.emit("心动模式已开启") }
                } else {
                    viewModelScope.launch { _errorMessageFlow.emit("没有找到相似歌曲") }
                }
            } catch (e: Exception) {
                Log.e(TAG, "开启心动模式失败", e)
                viewModelScope.launch { _errorMessageFlow.emit("开启心动模式失败: ${e.message}") }
            } finally {
                setLoading(false)
            }
        }
    }

    /**
     * 播放相似歌曲
     */
    fun playSimilarSong(position: Int) {
        val similarSongs = _similarSongsFlow.value
        if (similarSongs.isNotEmpty() && position < similarSongs.size) {
            // 播放选中的歌曲
            playerController.replaceAll(similarSongs, similarSongs[position])

            // 更新当前播放列表ID
            val songId = similarSongs[position].mediaId.toLongOrNull() ?: 0L
            currentPlaylistId = "similar_$songId"

            // 保存播放列表
            saveCurrentPlaylist()
        }
    }



    override fun onCleared() {
        super.onCleared()

        try {
            // 注销广播接收器
            getApplication<Application>().unregisterReceiver(newSongsReceiver)
        } catch (e: Exception) {
            Log.e(TAG, "注销广播接收器失败", e)
        }

        // 保存当前播放列表
        saveCurrentPlaylist()

        // 停止播放
        playerController.stop()
    }
}
